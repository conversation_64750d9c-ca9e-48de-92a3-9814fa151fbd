<template>
  <div class="special-goods-skeleton">
    <!-- 热区骨架屏 -->
    <div class="skeleton-hot-zone">
      <div class="skeleton-hot-zone-content"></div>
    </div>

    <!-- 商品滚动区域骨架屏 -->
    <div class="skeleton-scroll-wrapper">
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-title short"></div>
          <div class="skeleton-details">
            <div class="skeleton-price"></div>
            <div class="skeleton-sales"></div>
          </div>
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: @radius-4;
}

.special-goods-skeleton {
  height: 100%;
  display: flex;
  flex-direction: column;

  .skeleton-hot-zone {
    flex: 1;
    min-height: 40px;
    padding: @radius-12 @radius-12 0 @radius-12;

    .skeleton-hot-zone-content {
      .skeleton-base();
      width: 100%;
      height: 100%;
      min-height: 40px;
      border-radius: @radius-8;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.3) 75%);
    }
  }

  .skeleton-scroll-wrapper {
    display: flex;
    gap: @radius-12;
    overflow-x: auto;
    padding: @radius-12;
    scroll-behavior: smooth;
    width: 100%;
    .no-scrollbar();

    .skeleton-item {
      flex: 0 0 130px; // 与 ProductCardMini 的实际宽度保持一致
      background: @bg-color-white;
      border-radius: @radius-6; // 与 ProductCardMini 的 border-radius 保持一致
      overflow: hidden;
      cursor: pointer;
      // 计算总高度：图片120px + 内容约80px = 200px
      height: 200px;

      // 最后一个元素添加右边距，与实际布局保持一致
      &:last-child {
        margin-right: @radius-12;
      }

      .skeleton-image {
        .skeleton-base();
        width: 100%;
        height: 120px; // 与 ProductCardMini 图片高度保持一致
        border-radius: @radius-6 @radius-6 0 0;
        background-color: @bg-color-gray;
      }

      .skeleton-content {
        // 与 ProductCardMini 的 .goods-info padding: 8px 保持一致
        padding: @radius-8;

        .skeleton-title {
          .skeleton-base();
          // 与 ProductCardMini 的 .goods-name font-size: 12px 对应
          height: 12px;
          margin-bottom: @radius-6;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: @radius-4;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: @radius-4;
          gap: @radius-8;

          .skeleton-price {
            .skeleton-base();
            // 与 ProductCardMini 的 .goods-price font-size: 14px 对应
            height: 14px;
            width: 50px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            .skeleton-base();
            // 与 ProductCardMini 的 .goods-sales font-size: 11px 对应
            height: 11px;
            width: 40px;
            border-radius: @radius-2;
          }
        }

        .skeleton-spec {
          .skeleton-base();
          // 与 ProductCardMini 的 .goods-spec font-size: 11px 对应
          height: 11px;
          width: 75%;
          border-radius: @radius-2;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 375px) {
  .special-goods-skeleton {
    .skeleton-hot-zone {
      padding: @radius-8 @radius-8 0 @radius-8;

      .skeleton-hot-zone-content {
        min-height: 30px;
      }
    }

    .skeleton-scroll-wrapper {
      gap: @radius-8;
      padding: @radius-8;

      .skeleton-item {
        flex: 0 0 120px; // 移动端稍微缩小
        height: 190px;

        &:last-child {
          margin-right: @radius-8;
        }

        .skeleton-image {
          height: 110px;
        }

        .skeleton-content {
          padding: @radius-6;

          .skeleton-title {
            height: 11px;
            margin-bottom: @radius-4;

            &.short {
              margin-bottom: @radius-2;
            }
          }

          .skeleton-details {
            margin-bottom: @radius-2;
            gap: @radius-6;

            .skeleton-price {
              height: 13px;
              width: 45px;
            }

            .skeleton-sales {
              height: 10px;
              width: 35px;
            }
          }

          .skeleton-spec {
            height: 10px;
          }
        }
      }
    }
  }
}
</style>
