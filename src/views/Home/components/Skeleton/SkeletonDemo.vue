<template>
  <div class="skeleton-demo">
    <h2>特殊商品池骨架屏演示</h2>

    <!-- 演示容器 -->
    <div class="demo-container">
      <div class="horizontal-scroll-container demo-background">
        <SpecialGoodsSkeleton :skeleton-count="5" />
      </div>
    </div>

    <!-- 对比说明 -->
    <div class="demo-description">
      <h3>新骨架屏特点：</h3>
      <ul>
        <li>✅ 包含热区骨架屏，模拟实际布局结构</li>
        <li>✅ 匹配 ProductCardMini 尺寸（130px 宽，120px 图片高度）</li>
        <li>✅ 支持背景图片样式</li>
        <li>✅ 流畅的加载动画效果</li>
        <li>✅ 移动端适配优化</li>
        <li>✅ 与实际组件样式完全一致</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import SpecialGoodsSkeleton from './SpecialGoodsSkeleton.vue'
</script>

<style scoped lang="less">
.skeleton-demo {
  padding: 20px;
  background: @bg-color-gray;
  min-height: 100vh;

  h2 {
    color: @text-color-primary;
    margin-bottom: 20px;
    text-align: center;
  }

  .demo-container {
    margin-bottom: 30px;

    .horizontal-scroll-container {
      position: relative;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: @radius-12;
      margin: @radius-8 @radius-12;
      overflow: hidden;
      min-height: 200px;

      &.demo-background {
        // 演示背景图片效果
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    }
  }

  .demo-description {
    background: @bg-color-white;
    padding: 20px;
    border-radius: @radius-12;
    margin: 0 @radius-12;

    h3 {
      color: @text-color-primary;
      margin-bottom: 15px;
      font-size: @font-size-16;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: @text-color-secondary;
        font-size: @font-size-14;
        line-height: 1.6;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          background: @theme-color;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
