# 骨架屏组件说明

## SpecialGoodsSkeleton.vue

### 概述
专为特殊商品池区域设计的骨架屏组件，完美匹配实际布局结构和 ProductCardMini 组件样式。

### 特性
- ✅ **完整布局模拟**：包含热区和商品滚动区域的完整骨架屏
- ✅ **精确尺寸匹配**：与 ProductCardMini 组件尺寸完全一致
- ✅ **背景图片支持**：支持容器背景图片样式
- ✅ **流畅动画效果**：优化的加载动画，提升用户体验
- ✅ **响应式设计**：移动端适配优化
- ✅ **高度还原**：与实际组件样式保持一致

### 使用方法

```vue
<template>
  <div class="horizontal-scroll-container" :style="{ backgroundImage: backgroundImage }">
    <transition name="skeleton-fade" mode="out-in">
      <SpecialGoodsSkeleton v-if="loading" :skeleton-count="5" />
      <div v-else class="special-goods-content">
        <!-- 实际内容 -->
      </div>
    </transition>
  </div>
</template>

<script setup>
import SpecialGoodsSkeleton from '@views/Home/components/Skeleton/SpecialGoodsSkeleton.vue'
</script>
```

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| skeletonCount | Number | 5 | 骨架屏商品卡片数量 |

### 设计规范

#### 热区骨架屏
- **最小高度**：40px（桌面端）/ 30px（移动端）
- **背景**：半透明白色渐变效果
- **圆角**：8px
- **内边距**：12px（桌面端）/ 8px（移动端）

#### 商品卡片骨架屏
- **宽度**：130px（桌面端）/ 120px（移动端）
- **高度**：200px（桌面端）/ 190px（移动端）
- **图片区域**：120px（桌面端）/ 110px（移动端）
- **内容区域**：padding 8px（桌面端）/ 6px（移动端）
- **圆角**：6px（与 ProductCardMini 保持一致）

#### 动画效果
- **类型**：水平滑动渐变动画
- **持续时间**：1.2s
- **缓动函数**：ease-in-out
- **循环**：无限循环

### 与原组件对比

| 特性 | HorizontalScrollSkeleton | SpecialGoodsSkeleton |
|------|-------------------------|---------------------|
| 热区骨架屏 | ❌ 无 | ✅ 有 |
| 卡片宽度 | 160px | 130px |
| 图片高度 | 180px | 120px |
| 总高度 | 280px | 200px |
| 适配组件 | ProductCard | ProductCardMini |
| 背景图片支持 | ❌ 无 | ✅ 有 |

### 文件结构
```
src/views/Home/components/Skeleton/
├── SpecialGoodsSkeleton.vue    # 新的特殊商品池骨架屏
├── SkeletonDemo.vue           # 演示组件
├── HorizontalScrollSkeleton.vue # 原有的水平滚动骨架屏
└── README.md                  # 说明文档
```

### 注意事项
1. 确保容器具有 `horizontal-scroll-container` 类名
2. 使用 `skeleton-fade` 过渡动画以获得最佳效果
3. 骨架屏数量建议设置为 5-8 个，以获得最佳视觉效果
4. 移动端会自动适配较小的尺寸

### 更新记录
- **v1.0.0** (2025-08-07): 初始版本，支持热区和商品卡片骨架屏
